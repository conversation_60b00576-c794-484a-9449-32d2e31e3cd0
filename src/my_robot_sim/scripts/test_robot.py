#!/usr/bin/env python3

import rospy
from geometry_msgs.msg import Twist
from sensor_msgs.msg import LaserScan
from nav_msgs.msg import Odometry
import time

class RobotTester:
    def __init__(self):
        rospy.init_node('robot_tester', anonymous=True)
        
        # Publishers
        self.cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=10)
        
        # Subscribers
        self.scan_sub = rospy.Subscriber('/scan', LaserScan, self.scan_callback)
        self.odom_sub = rospy.Subscriber('/odom', Odometry, self.odom_callback)
        
        # Data storage
        self.latest_scan = None
        self.latest_odom = None
        
        rospy.loginfo("Robot Tester initialized")
        
    def scan_callback(self, msg):
        self.latest_scan = msg
        
    def odom_callback(self, msg):
        self.latest_odom = msg
        
    def test_movement(self):
        """Test robot movement capabilities"""
        rospy.loginfo("Testing robot movement...")
        
        # Test forward movement
        twist = Twist()
        twist.linear.x = 0.5
        twist.angular.z = 0.0
        
        rospy.loginfo("Moving forward...")
        for i in range(20):  # 2 seconds at 10Hz
            self.cmd_vel_pub.publish(twist)
            rospy.sleep(0.1)
            
        # Test rotation
        twist.linear.x = 0.0
        twist.angular.z = 0.5
        
        rospy.loginfo("Rotating...")
        for i in range(20):  # 2 seconds at 10Hz
            self.cmd_vel_pub.publish(twist)
            rospy.sleep(0.1)
            
        # Stop
        twist.linear.x = 0.0
        twist.angular.z = 0.0
        self.cmd_vel_pub.publish(twist)
        rospy.loginfo("Movement test completed")
        
    def test_sensors(self):
        """Test sensor data reception"""
        rospy.loginfo("Testing sensors...")
        
        # Wait for sensor data
        timeout = 10.0
        start_time = time.time()
        
        while (self.latest_scan is None or self.latest_odom is None) and (time.time() - start_time) < timeout:
            rospy.sleep(0.1)
            
        if self.latest_scan is None:
            rospy.logerr("Failed to receive laser scan data!")
            return False
            
        if self.latest_odom is None:
            rospy.logerr("Failed to receive odometry data!")
            return False
            
        # Test laser scan
        rospy.loginfo(f"Laser scan received:")
        rospy.loginfo(f"  - Range: {self.latest_scan.range_min:.2f}m to {self.latest_scan.range_max:.2f}m")
        rospy.loginfo(f"  - Angle: {self.latest_scan.angle_min:.2f} to {self.latest_scan.angle_max:.2f} rad")
        rospy.loginfo(f"  - Number of readings: {len(self.latest_scan.ranges)}")
        
        # Test odometry
        rospy.loginfo(f"Odometry received:")
        rospy.loginfo(f"  - Position: x={self.latest_odom.pose.pose.position.x:.2f}, y={self.latest_odom.pose.pose.position.y:.2f}")
        rospy.loginfo(f"  - Linear velocity: {self.latest_odom.twist.twist.linear.x:.2f} m/s")
        rospy.loginfo(f"  - Angular velocity: {self.latest_odom.twist.twist.angular.z:.2f} rad/s")
        
        rospy.loginfo("Sensor test completed successfully")
        return True
        
    def run_tests(self):
        """Run all tests"""
        rospy.loginfo("Starting robot tests...")
        
        # Test sensors first
        if not self.test_sensors():
            rospy.logerr("Sensor tests failed!")
            return
            
        # Test movement
        self.test_movement()
        
        rospy.loginfo("All tests completed successfully!")

if __name__ == '__main__':
    try:
        tester = RobotTester()
        rospy.sleep(2)  # Wait for everything to initialize
        tester.run_tests()
    except rospy.ROSInterruptException:
        pass
